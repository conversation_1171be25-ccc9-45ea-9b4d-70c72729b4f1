package corp.jamaro.jamaroservidor.app.dinero.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class DevolucionDinero {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private Double montoADevolver;//el monto en Soles ya que el sistema trabajar por defecto en soles a devolver

    @Relationship(type = "CON_DINERO_DEVUELTO")
    private Set<Dinero> dineroDevuelto;//es un set ya que puede ser una devolucion mixta yape y efectivo por ejemplo; en un inicio nisiquiera va a existir hasta que se haga la devolucion.

    private String iniciadoPor;//username que programo la devolucion
    private String devueltoPor;//username que devolvio el dinero

    private Instant creadoEl = Instant.now();
    private Instant devueltoEl;

    private Boolean estaDevuelto = false;// true (devuelto) false (no devuelto) para encontrar facilmente que dinero faltan devolver en caja.
}
