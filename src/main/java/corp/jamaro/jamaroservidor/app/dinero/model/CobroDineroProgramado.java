package corp.jamaro.jamaroservidor.app.dinero.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class CobroDineroProgramado {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private Double montoACobrar;//el monto en Soles ya que el sistema trabajar por defecto en soles.
    private Double montoRestante;//el monto restante a cobrar, se actualiza cada vez que se cobra dinero.

    @Relationship(type = "CON_DINERO_COBRADO")
    private Set<Dinero> dineroCobrados ;//pueden ser varios ya que hay varios metodos de pago y puede ser pago mixto,en un inicio nisiquiera va a existir hasta que se cobre, se programará un cobro y cuando se cobra recien se crea el dinero con esEntrada true.

    private String iniciadoPor;//username que programo el cobro

    private Instant creadoEl = Instant.now();
    private Instant fechaLimite;

    private Instant terminadoDeCobrarEl;

    private Boolean estaCobrado = false;// true (cobrado por completo) false (no cobrado por completo) para encontrar facilmente que dinero faltan cobrar en caja.

}
