package corp.jamaro.jamaroservidor.app.ventas.controller;

import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.dinero.model.enums.TipoMoneda;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.service.SaleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Controlador para gestionar las operaciones relacionadas con las ventas (Sale) a través de RSocket.
 *
 * Este controlador implementa el patrón de comunicación RSocket donde:
 * 1. El cliente envía solicitudes de actualización
 * 2. El servidor procesa las solicitudes y devuelve un resultado de éxito/error
 * 3. El cliente recibe las actualizaciones reales a través de su suscripción
 *
 * Todas las operaciones que modifican un Sale devuelven un OperationResponse con el resultado
 * de la operación, en lugar de devolver el Sale actualizado. El cliente recibirá el Sale
 * actualizado a través de su suscripción.
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class SaleController {

    private final SaleService saleService;


    @MessageMapping("sale.subscribe")
    public Flux<Sale> subscribeToSaleUpdates(UUID saleId) {
        log.info("Suscribiendo a actualizaciones del Sale con ID: {}", saleId);
        return saleService.subscribeToSaleUpdates(saleId);
    }

    /**
     * Actualiza el cliente de un Sale.
     *
     * Este endpoint permite a los clientes actualizar el Cliente asociado a un Sale.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos del cliente a actualizar (saleId y documento)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.updateCustomer")
    public Mono<OperationResponse> updateCustomer(UpdateCustomerRequest request) {
        log.info("Actualizando cliente en Sale con ID: {}, documento: {}",
                request.saleId(), request.documento());
        return saleService.updateCustomer(
                request.saleId(),
                request.documento())
                .map(success -> new OperationResponse(success,
                        success ? "Cliente actualizado correctamente" : "Error al actualizar el cliente"));
    }

    /**
     * Agrega un Item a un Sale como BienServicioCargado o actualiza la cantidad si ya existe.
     *
     * Este endpoint permite a los clientes agregar un Item a un Sale como BienServicioCargado.
     * Si el Item ya existe en el Sale, se incrementa la cantidad y se ignora el precioInicial.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * Si precioInicial es nulo, se usará el precioVentaPublico del Item (solo para nuevas relaciones).
     * Si cantidad es nula, se usará 1.0 o se incrementará en 1.0 si ya existe.
     *
     * @param request Datos del Item a agregar (saleId, codCompuesto, precioInicial, cantidad)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.addItemToSale")
    public Mono<OperationResponse> addItemToSale(AddItemToSaleRequest request) {
        log.info("Agregando Item a Sale con ID: {}, codCompuesto: {}",
                request.saleId(), request.codCompuesto());
        return saleService.addItemToSale(
                request.saleId(),
                request.codCompuesto(),
                request.precioInicial(),
                request.cantidad())
                .map(success -> new OperationResponse(success,
                        success ? "Item agregado correctamente" : "Error al agregar el item"));
    }


    /**
     * Actualiza el precio acordado y opcionalmente la cantidad de un BienServicioCargado específico en un Sale.
     *
     * Este endpoint permite a los clientes actualizar el precio acordado y opcionalmente la cantidad
     * de un BienServicioCargado en un Sale.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos para actualizar el precio (saleId, itemCodCompuesto, precioAcordado, cantidad opcional)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.updateBienServicioCargadoPrecioAcordado")
    public Mono<OperationResponse> updateBienServicioCargadoPrecioAcordado(UpdateBienServicioCargadoPrecioAcordadoRequest request) {
        log.info("Actualizando precio acordado de BienServicioCargado en Sale con ID: {}, itemCodCompuesto: {}, precioAcordado: {}",
                request.saleId(), request.itemCodCompuesto(), request.precioAcordado());
        return saleService.updateBienServicioCargadoPrecioAcordado(
                request.saleId(),
                request.itemCodCompuesto(),
                request.precioAcordado(),
                request.cantidad())
                .map(success -> new OperationResponse(success,
                        success ? "Precio acordado actualizado correctamente" : "Error al actualizar el precio acordado"));
    }



    /**
     * Elimina un BienServicioCargado específico de un Sale.
     *
     * Este endpoint permite a los clientes eliminar un BienServicioCargado de un Sale.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos para eliminar el BienServicioCargado (saleId, itemCodCompuesto)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.removeBienServicioCargado")
    public Mono<OperationResponse> removeBienServicioCargado(RemoveBienServicioCargadoRequest request) {
        log.info("Eliminando BienServicioCargado de Sale con ID: {}, itemCodCompuesto: {}",
                request.saleId(), request.itemCodCompuesto());
        return saleService.removeBienServicioCargado(
                request.saleId(),
                request.itemCodCompuesto())
                .map(success -> new OperationResponse(success,
                        success ? "BienServicioCargado eliminado correctamente" : "Error al eliminar el BienServicioCargado"));
    }

    /**
     * Agrega un pago (Dinero) a un Sale.
     *
     * Este endpoint permite a los clientes agregar un pago a un Sale.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos del pago a agregar (saleId, monto, tipoDePago, divisa, detalles)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.addDineroPagoCredito")
    public Mono<OperationResponse> addDineroPagoCredito(AddDineroPagoCreditoRequest request) {
        log.info("Agregando pago de crédito para Cliente con ID: {}, montoReal: {}",
                request.clienteId(), request.dinero().getMontoReal());
        return saleService.addDineroPagoCredito(request.clienteId(), request.dinero())
                .map(success -> new OperationResponse(success,
                        success ? "Pago de crédito agregado correctamente" : "Error al agregar el pago de crédito"));
    }

    /**
     * Agrega una devolución de dinero (Dinero) a un Sale.
     *
     * Este endpoint permite a los clientes agregar una devolución de dinero a un Sale.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos de la devolución a agregar (saleId, monto, tipoDePago, divisa, detalles)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.addDineroDevolucion")
    public Mono<OperationResponse> addDineroDevolucion(AddDineroDevolucionRequest request) {
        log.info("Agregando devolución a Sale con ID: {}, montoReal: {}",
                request.saleId(), request.dinero().getMontoReal());
        return saleService.addDineroDevolucion(request.saleId(), request.dinero())
                .map(success -> new OperationResponse(success,
                        success ? "Devolución agregada correctamente" : "Error al agregar la devolución"));
    }

    /**
     * Agrega un BienServicioDevuelto a un Sale, modificando o eliminando el BienServicioCargado correspondiente.
     *
     * Este endpoint permite a los clientes registrar la devolución de un bien o servicio.
     * Si la cantidad devuelta es igual a la cantidad cargada, se elimina el BienServicioCargado.
     * Si la cantidad devuelta es menor, se reduce la cantidad del BienServicioCargado.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos de la devolución (saleId, itemCodCompuesto, descripcionDelBienServicio, detalles, montoDevuelto, cantidad, motivo)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.addBienServicioDevuelto")
    public Mono<OperationResponse> addBienServicioDevuelto(AddBienServicioDevueltoRequest request) {
        log.info("Agregando BienServicioDevuelto a Sale con ID: {}, itemCodCompuesto: {}, cantidad: {}",
                request.saleId(), request.itemCodCompuesto(), request.cantidad());
        return saleService.addBienServicioDevuelto(
                request.saleId(),
                request.itemCodCompuesto(),
                request.descripcionDelBienServicio(),
                request.detalles(),
                request.montoDevuelto(),
                request.cantidad(),
                request.motivo())
                .map(success -> new OperationResponse(success,
                        success ? "Devolución de bien/servicio registrada correctamente" : "Error al registrar la devolución"));
    }

    /**
     * Elimina todas las relaciones BienServicioCargado de un Sale.
     *
     * Este endpoint permite a los clientes eliminar todos los items de un Sale.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos para eliminar todos los BienServicioCargado (saleId)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.removeAllBienServicioCargado")
    public Mono<OperationResponse> removeAllBienServicioCargado(RemoveAllBienServicioCargadoRequest request) {
        log.info("Eliminando todas las relaciones BienServicioCargado de Sale con ID: {}", request.saleId());
        return saleService.removeAllBienServicioCargado(request.saleId())
                .map(success -> new OperationResponse(success,
                        success ? "Todos los items eliminados correctamente" : "Error al eliminar los items"));
    }

    /**
     * Actualiza el tipo de venta de un Sale de PROFORMA a otro tipo (CONTADO, CREDITO, PEDIDO).
     *
     * Este endpoint permite a los clientes cambiar el tipo de venta de un Sale de PROFORMA a otro tipo.
     * Devuelve un OperationResponse con el resultado de la operación, no el Sale actualizado.
     * El cliente recibirá el Sale actualizado a través de su suscripción.
     *
     * @param request Datos para actualizar el tipo de venta (saleId, nuevoTipoVenta)
     * @return Mono con el resultado de la operación (success y mensaje)
     */
    @MessageMapping("sale.updateTipoVentaFromProforma")
    public Mono<OperationResponse> updateTipoVentaFromProforma(UpdateTipoVentaFromProformaRequest request) {
        log.info("Actualizando tipo de venta de Sale con ID: {} de PROFORMA a {}",
                request.saleId(), request.nuevoTipoVenta());
        return saleService.updateTipoVentaFromProforma(request.saleId(), request.nuevoTipoVenta())
                .map(success -> new OperationResponse(success,
                        success ? "Tipo de venta actualizado correctamente" : "Error al actualizar el tipo de venta"));
    }

    // Records para los parámetros de las solicitudes y respuestas

    /**
     * Datos para la solicitud de actualización de cliente.
     *
     * @param saleId ID del Sale a actualizar
     * @param documento Documento del cliente a asignar
     */
    public record UpdateCustomerRequest(UUID saleId, String documento) {}

    /**
     * Datos para la solicitud de agregar un Item a un Sale.
     *
     * @param saleId ID del Sale a actualizar
     * @param codCompuesto Código compuesto del Item a agregar
     * @param precioInicial Precio inicial (opcional)
     * @param cantidad Cantidad (opcional)
     */
    public record AddItemToSaleRequest(UUID saleId, String codCompuesto, Double precioInicial, Double cantidad) {}

    /**
     * Datos para la solicitud de actualizar el estado active de un BienServicioCargado.
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item en la relación
     * @param active Nuevo valor para la propiedad active
     */
    public record UpdateBienServicioCargadoActiveRequest(UUID saleId, String itemCodCompuesto, Boolean active) {}

    /**
     * Datos para la solicitud de actualizar el precio acordado y opcionalmente la cantidad de un BienServicioCargado.
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item en la relación
     * @param precioAcordado Nuevo precio acordado
     * @param cantidad Nueva cantidad (opcional)
     */
    public record UpdateBienServicioCargadoPrecioAcordadoRequest(UUID saleId, String itemCodCompuesto, Double precioAcordado, Double cantidad) {}

    /**
     * Datos para la solicitud de eliminar un BienServicioCargado.
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item en la relación a eliminar
     */
    public record RemoveBienServicioCargadoRequest(UUID saleId, String itemCodCompuesto) {}

    /**
     * Datos para la solicitud de agregar un pago de crédito.
     *
     * @param clienteId ID del Cliente
     * @param dinero Objeto Dinero parcialmente inicializado
     */
    public record AddDineroPagoCreditoRequest(UUID clienteId, Dinero dinero) {}

    /**
     * Datos para la solicitud de agregar una devolución de dinero (Dinero) a un Sale.
     *
     * @param saleId ID del Sale a actualizar
     * @param dinero Objeto Dinero parcialmente inicializado
     */
    public record AddDineroDevolucionRequest(UUID saleId, Dinero dinero) {}

    /**
     * Datos para la solicitud de agregar un BienServicioDevuelto a un Sale.
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item
     * @param descripcionDelBienServicio Descripción del bien o servicio (opcional)
     * @param detalles Detalles de la devolución
     * @param montoDevuelto Monto a devolver (opcional)
     * @param cantidad Cantidad a devolver
     * @param motivo Motivo de la devolución
     */
    public record AddBienServicioDevueltoRequest(UUID saleId, String itemCodCompuesto,
                                               String descripcionDelBienServicio, String detalles,
                                               Double montoDevuelto, Double cantidad, String motivo) {}

    /**
     * Datos para la solicitud de eliminar todas las relaciones BienServicioCargado de un Sale.
     *
     * @param saleId ID del Sale a actualizar
     */
    public record RemoveAllBienServicioCargadoRequest(UUID saleId) {}

    /**
     * Datos para la solicitud de actualizar el tipo de venta de un Sale de PROFORMA a otro tipo.
     *
     * @param saleId ID del Sale a actualizar
     * @param nuevoTipoVenta Nuevo tipo de venta (CONTADO, CREDITO, PEDIDO)
     */
    public record UpdateTipoVentaFromProformaRequest(UUID saleId, String nuevoTipoVenta) {}

    /**
     * Respuesta estándar para operaciones.
     *
     * @param success Indica si la operación fue exitosa
     * @param message Mensaje descriptivo del resultado
     */
    public record OperationResponse(boolean success, String message) {}
}
