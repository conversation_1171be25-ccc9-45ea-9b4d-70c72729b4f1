package corp.jamaro.jamaroservidor.app.ventas.controller;

import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.model.BienServicioDevuelto;
import corp.jamaro.jamaroservidor.app.ventas.service.SaleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Controlador para gestionar las operaciones relacionadas con las ventas (Sale) a través de RSocket.
 * Refactorizado para trabajar con la nueva estructura de nodos independientes.
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class SaleController {

    private final SaleService saleService;

    @MessageMapping("sale.subscribe")
    public Flux<Sale> subscribeToSaleUpdates(UUID saleId) {
        log.info("Suscribiendo a actualizaciones del Sale con ID: {}", saleId);
        return saleService.subscribeToSaleUpdates(saleId);
    }

    /**
     * Actualiza el Cliente de un Sale.
     */
    @MessageMapping("sale.updateCliente")
    public Mono<OperationResponse> updateCliente(UpdateClienteRequest request) {
        log.info("Actualizando Cliente en Sale con ID: {}, clienteId: {}",
                request.saleId(), request.clienteId());
        return saleService.updateCliente(request.saleId(), request.clienteId())
                .map(success -> new OperationResponse(success,
                        success ? "Cliente actualizado correctamente" : "Error al actualizar el cliente"));
    }

    /**
     * Agrega un Item a un Sale como BienServicioCargado o actualiza la cantidad si ya existe.
     */
    @MessageMapping("sale.addItemToSale")
    public Mono<OperationResponse> addItemToSale(AddItemToSaleRequest request) {
        log.info("Agregando Item a Sale con ID: {}, itemCodCompuesto: {}",
                request.saleId(), request.itemCodCompuesto());
        return saleService.addItemToSale(
                request.saleId(),
                request.itemCodCompuesto(),
                request.precioInicial(),
                request.cantidad())
                .map(success -> new OperationResponse(success,
                        success ? "Item agregado correctamente" : "Error al agregar el item"));
    }


    /**
     * Actualiza los campos de un BienServicioCargado.
     */
    @MessageMapping("sale.updateBienServicioCargado")
    public Mono<OperationResponse> updateBienServicioCargado(UpdateBienServicioCargadoRequest request) {
        log.info("Actualizando BienServicioCargado en Sale con ID: {}, itemCodCompuesto: {}, precioAcordado: {}",
                request.saleId(), request.itemCodCompuesto(), request.precioAcordado());
        return saleService.updateBienServicioCargado(
                request.saleId(),
                request.itemCodCompuesto(),
                request.precioAcordado(),
                request.cantidad(),
                request.descripcionDelBienServicio())
                .map(success -> new OperationResponse(success,
                        success ? "BienServicioCargado actualizado correctamente" : "Error al actualizar BienServicioCargado"));
    }



    /**
     * Elimina un BienServicioCargado específico.
     */
    @MessageMapping("sale.deleteBienServicioCargado")
    public Mono<OperationResponse> deleteBienServicioCargado(DeleteBienServicioCargadoRequest request) {
        log.info("Eliminando BienServicioCargado de Sale con ID: {}, bienServicioCargadoId: {}",
                request.saleId(), request.bienServicioCargadoId());
        return saleService.deleteBienServicioCargado(
                request.saleId(),
                request.bienServicioCargadoId())
                .map(success -> new OperationResponse(success,
                        success ? "BienServicioCargado eliminado correctamente" : "Error al eliminar el BienServicioCargado"));
    }

    /**
     * Elimina todos los nodos BienServicioCargado de un Sale.
     */
    @MessageMapping("sale.deleteAllBienServicioCargado")
    public Mono<OperationResponse> deleteAllBienServicioCargado(DeleteAllBienServicioCargadoRequest request) {
        log.info("Eliminando todos los BienServicioCargado de Sale con ID: {}", request.saleId());
        return saleService.deleteAllBienServicioCargado(request.saleId())
                .map(success -> new OperationResponse(success,
                        success ? "Todos los BienServicioCargado eliminados correctamente" : "Error al eliminar los BienServicioCargado"));
    }

    /**
     * Inicia una venta de contado.
     */
    @MessageMapping("sale.iniciarVentaContado")
    public Mono<OperationResponse> iniciarVentaContado(IniciarVentaContadoRequest request) {
        log.info("Iniciando venta de contado para Sale con ID: {}", request.saleId());
        return saleService.iniciarVentaContado(request.saleId())
                .map(success -> new OperationResponse(success,
                        success ? "Venta de contado iniciada correctamente" : "Error al iniciar venta de contado"));
    }

    /**
     * Inicia un BienServicioDevuelto.
     */
    @MessageMapping("sale.iniciarBienServicioDevuelto")
    public Mono<OperationResponse> iniciarBienServicioDevuelto(IniciarBienServicioDevueltoRequest request) {
        log.info("Iniciando BienServicioDevuelto para Sale con ID: {}, bienServicioCargadoId: {}",
                request.saleId(), request.bienServicioCargadoId());
        return saleService.iniciarBienServicioDevuelto(
                request.saleId(),
                request.bienServicioCargadoId(),
                request.bienServicioDevuelto())
                .map(success -> new OperationResponse(success,
                        success ? "BienServicioDevuelto iniciado correctamente" : "Error al iniciar BienServicioDevuelto"));
    }

    // Records para los parámetros de las solicitudes y respuestas

    /**
     * Datos para la solicitud de actualización de Cliente.
     */
    public record UpdateClienteRequest(UUID saleId, UUID clienteId) {}

    /**
     * Datos para la solicitud de agregar un Item a un Sale.
     */
    public record AddItemToSaleRequest(UUID saleId, String itemCodCompuesto, Double precioInicial, Double cantidad) {}

    /**
     * Datos para la solicitud de actualizar los campos de un BienServicioCargado.
     */
    public record UpdateBienServicioCargadoRequest(UUID saleId, String itemCodCompuesto, Double precioAcordado, Double cantidad, String descripcionDelBienServicio) {}

    /**
     * Datos para la solicitud de eliminar un BienServicioCargado.
     */
    public record DeleteBienServicioCargadoRequest(UUID saleId, UUID bienServicioCargadoId) {}

    /**
     * Datos para la solicitud de eliminar todos los BienServicioCargado de un Sale.
     */
    public record DeleteAllBienServicioCargadoRequest(UUID saleId) {}

    /**
     * Datos para la solicitud de iniciar venta de contado.
     */
    public record IniciarVentaContadoRequest(UUID saleId) {}

    /**
     * Datos para la solicitud de iniciar BienServicioDevuelto.
     */
    public record IniciarBienServicioDevueltoRequest(UUID saleId, UUID bienServicioCargadoId, BienServicioDevuelto bienServicioDevuelto) {}

    /**
     * Respuesta estándar para operaciones.
     */
    public record OperationResponse(boolean success, String message) {}
}
