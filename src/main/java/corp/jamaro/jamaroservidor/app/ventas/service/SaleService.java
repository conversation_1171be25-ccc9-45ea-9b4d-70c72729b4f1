package corp.jamaro.jamaroservidor.app.ventas.service;

import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import corp.jamaro.jamaroservidor.app.dinero.model.enums.TipoMoneda;
import corp.jamaro.jamaroservidor.app.dinero.repository.DineroRepository;
import corp.jamaro.jamaroservidor.app.repository.ClienteRepository;
import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.repository.SaleRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Servicio para gestionar las operaciones relacionadas con las ventas (Sale).
 *
 * Este servicio implementa el patrón de comunicación RSocket donde:
 * 1. El cliente envía solicitudes de actualización
 * 2. El servidor procesa las solicitudes y devuelve un resultado de éxito/error
 * 3. El cliente recibe las actualizaciones reales a través de su suscripción
 *
 * Todas las operaciones que modifican un Sale notifican a los clientes suscritos
 * a través del updateSink.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SaleService {

    private final SaleRepository saleRepository;
    private final ClienteRepository clienteRepository;
    private final ItemRepository itemRepository;
    private final DineroRepository dineroRepository;

    // Emisor para notificar actualizaciones vía RSocket
    // Utiliza multicast.directBestEffort() para enviar actualizaciones a múltiples suscriptores
    // sin bloquear si algún suscriptor es lento
    private final Sinks.Many<Sale> updateSink = Sinks.many().multicast().directBestEffort();

    /**
     * Permite a los clientes suscribirse a actualizaciones de un Sale específico.
     *
     * El flujo emitirá:
     * 1. El estado actual del Sale como primer elemento (con todas sus relaciones)
     * 2. Todas las actualizaciones futuras que se realicen sobre ese Sale
     *
     * Esta implementación usa una consulta Cypher optimizada para cargar todas las relaciones
     * del Sale en una sola operación de base de datos.
     *
     * @param saleId ID del Sale al que se quiere suscribir
     * @return Flux que emite el Sale actual y sus actualizaciones futuras
     */
    public Flux<Sale> subscribeToSaleUpdates(UUID saleId) {
        log.info("Suscribiendo a actualizaciones del Sale con ID: {}", saleId);

        // Primero obtenemos el estado actual del Sale con todas sus relaciones
        // usando nuestra consulta Cypher optimizada
        Mono<Sale> currentState = saleRepository.findSaleWithAllRelationships(saleId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId)));

        // Luego nos suscribimos al flujo de actualizaciones, filtrando solo las del Sale solicitado
        Flux<Sale> updates = updateSink.asFlux()
                .filter(sale -> sale.getId().equals(saleId));

        // Concatenamos el estado actual con las actualizaciones futuras
        return currentState.concatWith(updates);
    }



    /**
     * Emite una versión fresca del Sale cargada con todas sus relaciones.
     * Esto garantiza que los clientes reciban datos completos y actualizados,
     * incluyendo todas las relaciones (Marca, Producto, Grupo, etc.).
     *
     * @param saleId ID del Sale a emitir
     * @return Mono<Void> que completa cuando la operación termina
     */
    private Mono<Void> emitSaleWithAllRelationships(UUID saleId) {
        return saleRepository.findSaleWithAllRelationships(saleId)
                .doOnNext(freshSale -> {
                    log.debug("Emitiendo actualización para Sale con ID: {}", saleId);
                    updateSink.tryEmitNext(freshSale);
                })
                .then();
    }



    /**
     * Actualiza el Cliente de un Sale usando una consulta Cypher directa.
     *
     * Esta implementación es más eficiente porque:
     * 1. No carga el modelo completo de Sale y Cliente antes de la operación
     * 2. Actualiza la relación directamente en la base de datos
     * 3. Maneja la eliminación de relaciones existentes en la consulta Cypher
     *
     * @param saleId ID del Sale a actualizar
     * @param clienteId UUID del Cliente a asignar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> updateCliente(UUID saleId, UUID clienteId) {
        log.debug("Actualizando Cliente en Sale con ID: {}, clienteId: {}", saleId, clienteId);

        // Verificamos que el Sale exista
        Mono<Boolean> saleExistsMono = saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return Mono.just(true);
                });

        // Verificamos que el Cliente exista
        Mono<Boolean> clienteExistsMono = clienteRepository.existsById(clienteId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Cliente no encontrado con ID: " + clienteId));
                    }
                    return Mono.just(true);
                });

        // Ejecutamos las verificaciones y luego la consulta Cypher
        return Mono.zip(saleExistsMono, clienteExistsMono)
                .flatMap(tuple ->
                    // Usamos la consulta Cypher directa para actualizar el Cliente del Sale
                    saleRepository.updateCliente(saleId, clienteId)
                        // Emitimos una versión fresca con todas las relaciones
                        .flatMap(this::emitSaleWithAllRelationships)
                        .thenReturn(true)
                )
                .onErrorResume(e -> {
                    log.error("Error al actualizar Cliente en Sale: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Agrega un Item a un Sale como BienServicioCargado o actualiza la cantidad si ya existe.
     *
     * Esta implementación:
     * 1. Verifica si ya existe una relación BienServicioCargado para el Item
     * 2. Si existe, incrementa la cantidad existente (ignora el precioInicial)
     * 3. Si no existe, crea una nueva relación con los datos proporcionados
     * 4. Actualiza los montos totales del Sale (totalMontoInicial, totalMontoAcordado)
     *
     * @param saleId ID del Sale a actualizar
     * @param codCompuesto Código compuesto del Item a agregar
     * @param precioInicial Precio inicial (opcional, solo se usa si no existe la relación)
     * @param cantidad Cantidad (opcional, si es null se usa 1.0)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> addItemToSale(UUID saleId, String codCompuesto, Double precioInicial, Double cantidad) {
        log.debug("Agregando Item a Sale con ID: {}, codCompuesto: {}", saleId, codCompuesto);

        // Verificamos que el Sale exista
        Mono<Boolean> saleExistsMono = saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Sale no encontrado con ID: " + saleId));
                    }
                    return Mono.just(true);
                });

        // Verificamos que el Item exista
        Mono<Boolean> itemExistsMono = itemRepository.existsByCodCompuesto(codCompuesto)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Item no encontrado con codCompuesto: " + codCompuesto));
                    }
                    return Mono.just(true);
                });

        // Obtenemos el usuario actual para el campo cargadoPor
        Mono<String> usernameMono = SecurityUtils.getCurrentUser()
                .map(User::getUsername)
                .switchIfEmpty(Mono.error(new IllegalStateException("No se pudo obtener el usuario actual")));

        // Ejecutamos las verificaciones y luego verificamos si ya existe la relación
        return Mono.zip(saleExistsMono, itemExistsMono, usernameMono)
                .flatMap(tuple -> {
                    String username = tuple.getT3();

                    // Verificar si ya existe una relación BienServicioCargado para este Item
                    return saleRepository.existsBienServicioCargadoRelationship(saleId, codCompuesto)
                        .flatMap(exists -> {
                            if (exists) {
                                log.debug("Ya existe una relación BienServicioCargado para el Item {}, actualizando cantidad", codCompuesto);
                                // Si ya existe, incrementamos la cantidad
                                return saleRepository.updateBienServicioCargadoCantidad(saleId, codCompuesto, cantidad)
                                    // Calculamos y actualizamos los montos totales
                                    .flatMap(id -> saleRepository.calculateAndUpdateMontosTotales(saleId))
                                    // Emitimos una versión fresca con todas las relaciones
                                    .flatMap(sale -> emitSaleWithAllRelationships(sale.getId()))
                                    .thenReturn(true);
                            } else {
                                log.debug("Creando nueva relación BienServicioCargado para el Item {}", codCompuesto);
                                // Si no existe, creamos una nueva relación
                                return saleRepository.addItemToSale(
                                        saleId,
                                        codCompuesto,
                                        username,
                                        precioInicial,
                                        cantidad,
                                        Instant.now()
                                )
                                // Calculamos y actualizamos los montos totales
                                .flatMap(id -> saleRepository.calculateAndUpdateMontosTotales(saleId))
                                // Emitimos una versión fresca con todas las relaciones
                                .flatMap(sale -> emitSaleWithAllRelationships(sale.getId()))
                                .thenReturn(true);
                            }
                        });
                })
                .onErrorResume(e -> {
                    log.error("Error al agregar Item a Sale: {}", e.getMessage());
                    return Mono.just(false);
                });
    }



    /**
     * Actualiza el precio acordado y opcionalmente la cantidad de un BienServicioCargado específico en un Sale.
     *
     * Esta implementación:
     * 1. Actualiza el precio acordado y opcionalmente la cantidad del BienServicioCargado
     * 2. Recalcula y actualiza los montos totales del Sale
     * 3. Emite una actualización a los clientes suscritos
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item en la relación
     * @param precioAcordado Nuevo precio acordado
     * @param cantidad Nueva cantidad (opcional, si es null no se actualiza)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> updateBienServicioCargadoPrecioAcordado(UUID saleId, String itemCodCompuesto, Double precioAcordado, Double cantidad) {
        log.debug("Actualizando precio acordado de BienServicioCargado en Sale con ID: {}, itemCodCompuesto: {}, precioAcordado: {}, cantidad: {}",
                 saleId, itemCodCompuesto, precioAcordado, cantidad);

        // Mono para la operación de actualización
        Mono<UUID> updateMono;

        // Si se proporciona cantidad, actualizamos ambos valores
        if (cantidad != null) {
            updateMono = saleRepository.updateBienServicioCargadoPrecioAcordadoYCantidad(saleId, itemCodCompuesto, precioAcordado, cantidad);
        } else {
            // Si no se proporciona cantidad, solo actualizamos el precio acordado
            updateMono = saleRepository.updateBienServicioCargadoPrecioAcordado(saleId, itemCodCompuesto, precioAcordado);
        }

        // Procesamos el resultado de la actualización
        return updateMono
            // Calculamos y actualizamos los montos totales
            .flatMap(id -> saleRepository.calculateAndUpdateMontosTotales(saleId))
            // Emitimos una versión fresca con todas las relaciones
            .flatMap(sale -> emitSaleWithAllRelationships(sale.getId()))
            .thenReturn(true)
            .onErrorResume(e -> {
                log.error("Error al actualizar precio acordado de BienServicioCargado: {}", e.getMessage());
                return Mono.just(false);
            });
    }

    /**
     * Elimina un BienServicioCargado específico de un Sale y actualiza los montos totales.
     *
     * Esta implementación:
     * 1. Elimina la relación BienServicioCargado
     * 2. Recalcula y actualiza los montos totales del Sale
     * 3. Emite una actualización a los clientes suscritos
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item en la relación a eliminar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> removeBienServicioCargado(UUID saleId, String itemCodCompuesto) {
        log.debug("Eliminando BienServicioCargado de Sale con ID: {}, itemCodCompuesto: {}", saleId, itemCodCompuesto);

        return saleRepository.removeBienServicioCargado(saleId, itemCodCompuesto)
            // Calculamos y actualizamos los montos totales
            .flatMap(id -> saleRepository.calculateAndUpdateMontosTotales(saleId))
            // Emitimos una versión fresca con todas las relaciones
            .flatMap(sale -> emitSaleWithAllRelationships(sale.getId()))
            .thenReturn(true)
            .onErrorResume(e -> {
                log.error("Error al eliminar BienServicioCargado: {}", e.getMessage());
                return Mono.just(false);
            });
    }

    /**
     * Agrega un pago de crédito que se distribuye automáticamente entre los Sales de crédito pendientes del Cliente.
     *
     * Esta implementación:
     * 1. Verifica que el Cliente exista
     * 2. Obtiene el usuario actual para el campo recibidoPor
     * 3. Completa el objeto Dinero con los datos faltantes
     * 4. Guarda el objeto Dinero usando DineroRepository
     * 5. Busca todos los Sales de crédito pendientes del Cliente (FIFO)
     * 6. Distribuye el pago entre los Sales usando el método FIFO
     * 7. Actualiza el estado de los Sales (estaPagado, totalRestante)
     * 8. Crea las relaciones CON_DINERO_PAGADO correspondientes
     * 9. Emite actualizaciones a los clientes suscritos
     *
     * @param clienteId ID del Cliente
     * @param dinero Objeto Dinero parcialmente inicializado (tipoMoneda, montoRecibido, factorDeCambio, montoReal, tipoDePago, detalles, esEntrada=true)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> addDineroPagoCredito(UUID clienteId, Dinero dinero) {
        log.debug("Agregando pago de crédito para Cliente con ID: {}, montoReal: {}",
                 clienteId, dinero.getMontoReal());

        // Verificar que el Cliente exista
        Mono<Boolean> clienteExistsMono = clienteRepository.existsById(clienteId)
                .flatMap(exists -> {
                    if (!exists) {
                        log.error("No se encontró el Cliente con ID: {}", clienteId);
                        return Mono.error(new IllegalArgumentException("No existe el Cliente con el ID proporcionado"));
                    }
                    return Mono.just(true);
                });

        // Obtener el usuario actual para el campo recibidoPor
        Mono<String> usernameMono = SecurityUtils.getCurrentUser()
                .map(User::getUsername)
                .switchIfEmpty(Mono.error(new IllegalStateException("No se pudo obtener el usuario actual")));

        // Ejecutar las verificaciones y luego procesar el pago
        return Mono.zip(clienteExistsMono, usernameMono)
                .flatMap(tuple -> {
                    String username = tuple.getT2();

                    // Completar el objeto Dinero
                    dinero.setRecibidoPor(username);
                    dinero.setEsEntrada(true); // Es un ingreso de dinero

                    // Guardar el Dinero usando el repositorio
                    return dineroRepository.save(dinero)
                        .flatMap(savedDinero -> {
                            // Buscar todos los Sales de crédito pendientes del Cliente (FIFO)
                            return saleRepository.findCreditSalesByClienteNotPaid(clienteId)
                                .collectList()
                                .flatMap(creditSales -> {
                                    if (creditSales.isEmpty()) {
                                        log.warn("No se encontraron Sales de crédito pendientes para el Cliente con ID: {}", clienteId);
                                        return Mono.just(true);
                                    }

                                    // Distribuir el pago usando FIFO
                                    return distributePagoFIFO(savedDinero, creditSales);
                                });
                        });
                })
                .onErrorResume(e -> {
                    log.error("Error al agregar pago de crédito: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Agrega una devolución de dinero (Dinero) a un Sale.
     *
     * Esta implementación:
     * 1. Verifica que el Sale exista
     * 2. Obtiene el usuario actual para el campo recibidoPor
     * 3. Completa el objeto Dinero con los datos faltantes
     * 4. Establece esEntrada = false (es una salida de dinero)
     * 5. Guarda el objeto Dinero usando DineroRepository
     * 6. Crea una relación CON_DINERO_DEVUELTO entre el Sale y el Dinero
     * 7. Emite una actualización a los clientes suscritos
     *
     * @param saleId ID del Sale a actualizar
     * @param dinero Objeto Dinero parcialmente inicializado (tipoMoneda, monto, factorDeCambio, montoReal, tipoDePago, detalles)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> addDineroDevolucion(UUID saleId, Dinero dinero) {
        log.debug("Agregando devolución a Sale con ID: {}, montoReal: {}",
                 saleId, dinero.getMontoReal());

        // Verificar que el Sale exista
        Mono<Boolean> saleExistsMono = saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        log.error("No se encontró el Sale con ID: {}", saleId);
                        return Mono.error(new IllegalArgumentException("No existe el Sale con el ID proporcionado"));
                    }
                    return Mono.just(true);
                });

        // Obtener el usuario actual para el campo recibidoPor
        Mono<String> usernameMono = SecurityUtils.getCurrentUser()
                .map(User::getUsername)
                .switchIfEmpty(Mono.error(new IllegalStateException("No se pudo obtener el usuario actual")));

        // Ejecutar las verificaciones y luego crear y guardar el Dinero
        return Mono.zip(saleExistsMono, usernameMono)
                .flatMap(tuple -> {
                    String username = tuple.getT2();

                    // Completar el objeto Dinero
                    dinero.setRecibidoPor(username);
                    dinero.setEsEntrada(false); // Es una salida de dinero (devolución)

                    // Guardar el Dinero usando el repositorio
                    return dineroRepository.save(dinero)
                        .flatMap(savedDinero -> {
                            // Crear la relación entre Sale y Dinero
                            return saleRepository.createDineroDevolucionRelationship(saleId, savedDinero.getId())
                                // Emitir una versión fresca con todas las relaciones
                                .flatMap(id -> emitSaleWithAllRelationships(saleId))
                                .thenReturn(true);
                        });
                })
                .onErrorResume(e -> {
                    log.error("Error al agregar devolución a Sale: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Agrega un BienServicioDevuelto a un Sale, modificando o eliminando el BienServicioCargado correspondiente.
     *
     * Esta implementación:
     * 1. Verifica que el Sale exista
     * 2. Verifica que exista un BienServicioCargado con el itemCodCompuesto proporcionado
     * 3. Obtiene los detalles del BienServicioCargado (precioAcordado, cantidad, descripción)
     * 4. Obtiene el usuario actual para el campo devueltoPor
     * 5. Crea una relación BienServicioDevuelto con los datos proporcionados
     * 6. Actualiza la cantidad del BienServicioCargado o lo elimina si la cantidad devuelta es igual a la cargada
     * 7. Recalcula y actualiza los montos totales
     * 8. Emite una actualización a los clientes suscritos
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item
     * @param descripcionDelBienServicio Descripción del bien o servicio (opcional, si es null se usa la del BienServicioCargado)
     * @param detalles Detalles de la devolución
     * @param montoDevuelto Monto a devolver (opcional, si es null se usa el precioAcordado del BienServicioCargado)
     * @param cantidad Cantidad a devolver
     * @param motivo Motivo de la devolución
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> addBienServicioDevuelto(UUID saleId, String itemCodCompuesto,
                                              String descripcionDelBienServicio, String detalles,
                                              Double montoDevuelto, Double cantidad, String motivo) {
        log.debug("Agregando BienServicioDevuelto a Sale con ID: {}, itemCodCompuesto: {}, cantidad: {}",
                 saleId, itemCodCompuesto, cantidad);

        // Verificar que el Sale exista
        Mono<Boolean> saleExistsMono = saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        log.error("No se encontró el Sale con ID: {}", saleId);
                        return Mono.error(new IllegalArgumentException("No existe el Sale con el ID proporcionado"));
                    }
                    return Mono.just(true);
                });

        // Verificar que exista un BienServicioCargado con el itemCodCompuesto proporcionado
        Mono<Boolean> bienServicioCargadoExistsMono = saleRepository.existsBienServicioCargadoRelationship(saleId, itemCodCompuesto)
                .flatMap(exists -> {
                    if (!exists) {
                        log.error("No se encontró un BienServicioCargado con itemCodCompuesto: {} en el Sale con ID: {}",
                                itemCodCompuesto, saleId);
                        return Mono.error(new IllegalArgumentException("No existe un BienServicioCargado con el código compuesto proporcionado"));
                    }
                    return Mono.just(true);
                });

        // Obtener los detalles del BienServicioCargado
        Mono<Map<String, Object>> bienServicioCargadoDetailsMono = saleRepository.getBienServicioCargadoDetails(saleId, itemCodCompuesto);

        // Obtener el usuario actual para el campo devueltoPor
        Mono<String> usernameMono = SecurityUtils.getCurrentUser()
                .map(User::getUsername)
                .switchIfEmpty(Mono.error(new IllegalStateException("No se pudo obtener el usuario actual")));

        // Ejecutar las verificaciones y luego crear el BienServicioDevuelto
        return Mono.zip(saleExistsMono, bienServicioCargadoExistsMono, bienServicioCargadoDetailsMono, usernameMono)
                .flatMap(tuple -> {
                    Map<String, Object> details = tuple.getT3();
                    String username = tuple.getT4();

                    // Obtener valores del BienServicioCargado
                    Double bscCantidad = (Double) details.get("cantidad");
                    Double bscPrecioAcordado = (Double) details.get("precioAcordado");
                    String bscDescripcion = (String) details.get("descripcionDelBienServicio");

                    // Validar que la cantidad a devolver no sea mayor que la cantidad cargada
                    if (cantidad > bscCantidad) {
                        return Mono.error(new IllegalArgumentException(
                                "La cantidad a devolver (" + cantidad + ") no puede ser mayor que la cantidad cargada (" + bscCantidad + ")"));
                    }

                    // Usar valores por defecto si no se proporcionan
                    String finalDescripcion = (descripcionDelBienServicio == null || descripcionDelBienServicio.isEmpty())
                            ? bscDescripcion : descripcionDelBienServicio;
                    Double finalMontoDevuelto = (montoDevuelto == null) ? bscPrecioAcordado : montoDevuelto;

                    // Calcular la nueva cantidad para el BienServicioCargado
                    Double newCantidad = bscCantidad - cantidad;

                    // Crear la relación BienServicioDevuelto
                    return saleRepository.createBienServicioDevueltoRelationship(
                            saleId, itemCodCompuesto, username, finalDescripcion,
                            detalles, finalMontoDevuelto, cantidad, motivo, Instant.now())
                        .flatMap(id -> {
                            // Actualizar o eliminar el BienServicioCargado según corresponda
                            return saleRepository.updateOrRemoveBienServicioCargadoCantidad(saleId, itemCodCompuesto, newCantidad);
                        })
                        // Calculamos y actualizamos los montos totales
                        .flatMap(id -> saleRepository.calculateAndUpdateMontosTotales(saleId))
                        // Emitimos una versión fresca con todas las relaciones
                        .flatMap(sale -> emitSaleWithAllRelationships(sale.getId()))
                        .thenReturn(true);
                })
                .onErrorResume(e -> {
                    log.error("Error al agregar BienServicioDevuelto: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Elimina todas las relaciones BienServicioCargado de un Sale.
     *
     * Esta implementación:
     * 1. Verifica que el Sale exista
     * 2. Elimina todas las relaciones BienServicioCargado
     * 3. Establece totalMontoInicial, totalMontoAcordado y totalRestante a 0.0
     * 4. Emite una actualización a los clientes suscritos
     *
     * @param saleId ID del Sale a actualizar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> removeAllBienServicioCargado(UUID saleId) {
        log.debug("Eliminando todas las relaciones BienServicioCargado de Sale con ID: {}", saleId);

        // Verificar que el Sale exista
        return saleRepository.existsById(saleId)
                .flatMap(exists -> {
                    if (!exists) {
                        log.error("No se encontró el Sale con ID: {}", saleId);
                        return Mono.error(new IllegalArgumentException("No existe el Sale con el ID proporcionado"));
                    }

                    // Eliminar todas las relaciones BienServicioCargado
                    return saleRepository.removeAllBienServicioCargado(saleId)
                        // Emitimos una versión fresca con todas las relaciones
                        .flatMap(sale -> emitSaleWithAllRelationships(sale.getId()))
                        .thenReturn(true);
                })
                .onErrorResume(e -> {
                    log.error("Error al eliminar todas las relaciones BienServicioCargado: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Actualiza el tipo de venta de un Sale de PROFORMA a otro tipo (CONTADO, CREDITO, PEDIDO).
     *
     * Esta implementación:
     * 1. Verifica que el Sale exista
     * 2. Verifica que el tipoVenta actual sea PROFORMA
     * 3. Actualiza el tipoVenta al nuevo valor
     * 4. Establece totalRestante igual a totalMontoAcordado
     * 5. Emite una actualización a los clientes suscritos
     *
     * @param saleId ID del Sale a actualizar
     * @param nuevoTipoVenta Nuevo tipo de venta (CONTADO, CREDITO, PEDIDO)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<Boolean> updateTipoVentaFromProforma(UUID saleId, String nuevoTipoVenta) {
        log.debug("Actualizando tipo de venta de Sale con ID: {} de PROFORMA a {}", saleId, nuevoTipoVenta);

        // Validar que el nuevo tipo de venta sea válido
        if (!Arrays.asList("CONTADO", "CREDITO", "PEDIDO").contains(nuevoTipoVenta)) {
            return Mono.error(new IllegalArgumentException("El tipo de venta debe ser CONTADO, CREDITO o PEDIDO"));
        }

        // Verificar que el Sale exista
        return saleRepository.findById(saleId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("No existe el Sale con el ID proporcionado")))
                .flatMap(sale -> {
                    // Verificar que el tipo de venta actual sea PROFORMA
                    if (!"PROFORMA".equals(sale.getTipoVenta())) {
                        return Mono.error(new IllegalArgumentException("El Sale no es de tipo PROFORMA"));
                    }

                    // Actualizar el tipo de venta
                    return saleRepository.updateTipoVentaFromProforma(saleId, nuevoTipoVenta)
                        // Emitimos una versión fresca con todas las relaciones
                        .flatMap(updatedSale -> emitSaleWithAllRelationships(updatedSale.getId()))
                        .thenReturn(true);
                })
                .onErrorResume(e -> {
                    log.error("Error al actualizar tipo de venta: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

    /**
     * Distribuye un pago entre múltiples Sales de crédito usando el método FIFO (First In First Out).
     *
     * Esta implementación:
     * 1. Itera por los Sales ordenados por fecha de creación (FIFO)
     * 2. Para cada Sale, aplica el pago disponible
     * 3. Si el pago cubre completamente el totalRestante, marca el Sale como pagado
     * 4. Si el pago es parcial, actualiza el totalRestante
     * 5. Crea las relaciones CON_DINERO_PAGADO correspondientes
     * 6. Emite actualizaciones para cada Sale modificado
     *
     * @param dinero Objeto Dinero guardado
     * @param creditSales Lista de Sales de crédito ordenados por fecha (FIFO)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    private Mono<Boolean> distributePagoFIFO(Dinero dinero, List<Sale> creditSales) {
        // Usar AtomicReference para hacer la variable mutable en el contexto reactivo
        AtomicReference<Double> montoRestanteRef = new AtomicReference<>(dinero.getMontoReal());

        // Procesar cada Sale secuencialmente
        return Flux.fromIterable(creditSales)
                .concatMap(sale -> {
                    Double montoRestante = montoRestanteRef.get();
                    if (montoRestante <= 0) {
                        return Mono.just(sale); // No hay más dinero para distribuir
                    }

                    Double totalRestanteSale = sale.getTotalRestante();

                    if (montoRestante >= totalRestanteSale) {
                        // El pago cubre completamente este Sale
                        montoRestanteRef.set(montoRestante - totalRestanteSale);

                        // Actualizar el Sale: totalRestante = 0, estaPagado = true
                        return saleRepository.updateMontosTotales(
                                sale.getId(),
                                sale.getTotalMontoInicial(),
                                sale.getTotalMontoAcordado(),
                                0.0)
                            .flatMap(updatedSale -> saleRepository.updateEstaPagado(sale.getId(), true))
                            .flatMap(updatedSale -> {
                                // Crear relación CON_DINERO_PAGADO
                                return saleRepository.createDineroPagoRelationship(sale.getId(), dinero.getId())
                                    .flatMap(id -> emitSaleWithAllRelationships(sale.getId()))
                                    .thenReturn(sale);
                            });
                    } else {
                        // El pago es parcial para este Sale
                        Double nuevoTotalRestante = totalRestanteSale - montoRestante;
                        montoRestanteRef.set(0.0); // Se agota el dinero

                        // Actualizar el Sale: totalRestante = nuevoTotalRestante, estaPagado = false
                        return saleRepository.updateMontosTotales(
                                sale.getId(),
                                sale.getTotalMontoInicial(),
                                sale.getTotalMontoAcordado(),
                                nuevoTotalRestante)
                            .flatMap(updatedSale -> {
                                // Crear relación CON_DINERO_PAGADO
                                return saleRepository.createDineroPagoRelationship(sale.getId(), dinero.getId())
                                    .flatMap(id -> emitSaleWithAllRelationships(sale.getId()))
                                    .thenReturn(sale);
                            });
                    }
                })
                .then(Mono.just(true))
                .onErrorResume(e -> {
                    log.error("Error al distribuir pago FIFO: {}", e.getMessage());
                    return Mono.just(false);
                });
    }

}
