package corp.jamaro.jamaroservidor.app.ventas.model;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class BienServicioCargado {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "CON_ITEM_CARGADO")
    private Item item;// cuando es pedido este puede llegar a ser nulo o vacio
    // en un futuro puedo cargar nodos tipo servicio o tipo pedido u otros.

    private String cargadoPor; // username que agrego este item o servicio o pedido

    private Double precioInicial;// el precio unitario con el que fue cargado generalmente el que se muestra en sistema, pero voy a poner maneras de cargar otros precios para negociar mejor
    private Double cantidad;

    private Double precioAcordado;//precio unitario acordado

    private Double montoAcordado;//monto total que pago por la cantidad del item. (precioAcordado * cantidad)


    // en caso exista un Item cargado será la misma descripcion del item, en caso no exista será la descripcion del item pedido
    //o un item general cuya descripcion se rellena en el momento.
    private String descripcionDelBienServicio;// se usa en caso de ventas del tipo pedido para rellenar la descripcion del item pedido

    private Instant createdAt= Instant.now();
}
