package corp.jamaro.jamaroservidor.app.ventas.model;

import corp.jamaro.jamaroservidor.app.dinero.model.CobroDineroProgramado;
import corp.jamaro.jamaroservidor.app.dinero.model.DevolucionDinero;
import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.dinero.model.Dinero;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class Sale {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String iniciadaPor;//username del user que inició la venta

    @Relationship(type = "CON_CLIENTE")
    private Cliente cliente;

    @Relationship(type = "CON_BIEN_SERVICIO_CARGADO")
    private List<BienServicioCargado> bienServicioCargados;

    @Relationship(type = "CON_BIEN_SERVICIO_DEVUELTO")
    private List<BienServicioDevuelto> bienServicioDevueltos;

    @Relationship(type = "CON_DINERO_COBRADO")
    private Set<CobroDineroProgramado> dineroCobros;

    @Relationship(type = "CON_DINERO_DEVUELTO")
    private Set<DevolucionDinero> dineroDevoluciones;

    private Double totalMontoInicial; // el monto total antes de los descuentos en soles
    private Double totalMontoAcordado; //el monto total acordado a pagar en Soles

    private Double totalRestante;// el monto total restante a pagar sirve para credito y para pedido cuando fue al contado es cero

    private Boolean estaPagadoEntregado=false; //para saber si terminó de ser pagado o entregado

    private TipoVenta tipoVenta = TipoVenta.PROFORMA;

    private Instant createdAt=Instant.now();


    //la moneda que usa por defecto el sistema es Soles no olvidar
    public enum TipoVenta {
        PROFORMA,
        CONTADO,
        CREDITO,
        PEDIDO
    }

}

