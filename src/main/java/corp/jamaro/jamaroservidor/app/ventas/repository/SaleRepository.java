package corp.jamaro.jamaroservidor.app.ventas.repository;

import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface SaleRepository extends ReactiveNeo4jRepository<Sale, UUID> {

    /**
     * Encuentra un Sale con todas sus relaciones para emisión de actualizaciones.
     * Incluye: Cliente, BienServicioCargado con Items (y sus relaciones con Marca, Producto, Grupo),
     * BienServicioDevuelto, CobroDineroProgramado, DevolucionDinero.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           OPTIONAL MATCH (s)-[:CON_CLIENTE]->(c:<PERSON><PERSON><PERSON>)
           OPTIONAL MATCH (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           OPTIONAL MATCH (bsc)-[:CON_ITEM_CARGADO]->(i:Item)
           OPTIONAL MATCH (i)-[:ITEM_PARTE_DEL_PRODUCTO]->(p:Producto)
           OPTIONAL MATCH (p)-[:PRODUCTO_PERTENECE_AL_GRUPO]->(g:Grupo)
           OPTIONAL MATCH (i)-[:CON_MARCA]->(m:Marca)
           OPTIONAL MATCH (s)-[:CON_BIEN_SERVICIO_DEVUELTO]->(bsd:BienServicioDevuelto)
           OPTIONAL MATCH (bsd)-[:CON_ITEM_DEVUELTO]->(id:Item)
           OPTIONAL MATCH (s)-[:CON_DINERO_COBRADO]->(cdp:CobroDineroProgramado)
           OPTIONAL MATCH (cdp)-[:CON_DINERO_COBRADO]->(dc:Dinero)
           OPTIONAL MATCH (s)-[:CON_DINERO_DEVUELTO]->(dd:DevolucionDinero)
           OPTIONAL MATCH (dd)-[:CON_DINERO_DEVUELTO]->(ddev:Dinero)
           RETURN s, c,
                  collect(DISTINCT bsc) as bienServicioCargados,
                  collect(DISTINCT i) as items,
                  collect(DISTINCT p) as productos,
                  collect(DISTINCT g) as grupos,
                  collect(DISTINCT m) as marcas,
                  collect(DISTINCT bsd) as bienServicioDevueltos,
                  collect(DISTINCT id) as itemsDevueltos,
                  collect(DISTINCT cdp) as dineroCobros,
                  collect(DISTINCT dc) as dinerosCobrados,
                  collect(DISTINCT dd) as dineroDevoluciones,
                  collect(DISTINCT ddev) as dinerosDevueltos
           """)
    Mono<Sale> findSaleWithAllRelationships(UUID saleId);

    /**
     * Actualiza el Cliente de un Sale.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           OPTIONAL MATCH (s)-[r:CON_CLIENTE]->(:Cliente)
           DELETE r
           WITH s
           MATCH (c:Cliente) WHERE c.id = $clienteId
           CREATE (s)-[:CON_CLIENTE]->(c)
           RETURN s.id
           """)
    Mono<UUID> updateCliente(UUID saleId, UUID clienteId);

    /**
     * Verifica si existe un BienServicioCargado para un Item específico en un Sale.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE s.id = $saleId AND i.codCompuesto = $itemCodCompuesto
           RETURN count(bsc) > 0
           """)
    Mono<Boolean> existsBienServicioCargadoForItem(UUID saleId, String itemCodCompuesto);

    /**
     * Agrega un Item a un Sale como BienServicioCargado.
     * Si precioInicial es null, usa el precioVentaPublico del Item.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           MATCH (i:Item) WHERE i.codCompuesto = $itemCodCompuesto
           WITH s, i, CASE WHEN $precioInicial IS NULL THEN i.precioVentaPublico ELSE $precioInicial END as finalPrecioInicial
           CREATE (bsc:BienServicioCargado {
               id: randomUUID(),
               cargadoPor: $cargadoPor,
               precioInicial: finalPrecioInicial,
               cantidad: $cantidad,
               precioAcordado: finalPrecioInicial,
               montoAcordado: finalPrecioInicial * $cantidad,
               descripcionDelBienServicio: i.descripcion,
               createdAt: datetime()
           })
           CREATE (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc)
           CREATE (bsc)-[:CON_ITEM_CARGADO]->(i)
           RETURN bsc.id
           """)
    Mono<UUID> addItemToSale(UUID saleId, String itemCodCompuesto, String cargadoPor,
                            Double precioInicial, Double cantidad);

    /**
     * Actualiza la cantidad de un BienServicioCargado existente.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE s.id = $saleId AND i.codCompuesto = $itemCodCompuesto
           SET bsc.cantidad = bsc.cantidad + $cantidadIncremento,
               bsc.montoAcordado = bsc.precioInicial * (bsc.cantidad + $cantidadIncremento)
           RETURN bsc.id
           """)
    Mono<UUID> incrementBienServicioCargadoCantidad(UUID saleId, String itemCodCompuesto, Double cantidadIncremento);

    /**
     * Actualiza el precio acordado y cantidad de un BienServicioCargado.
     */
    @Query("""
           MATCH (s:Sale)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE s.id = $saleId AND i.codCompuesto = $itemCodCompuesto
           SET bsc.montoAcordado = $montoAcordado
           SET bsc.cantidad = CASE WHEN $cantidad IS NOT NULL THEN $cantidad ELSE bsc.cantidad END
           RETURN bsc.id
           """)
    Mono<UUID> updateBienServicioCargadoPrecioAcordado(UUID saleId, String itemCodCompuesto,
                                                      Double montoAcordado, Double cantidad);

    /**
     * Elimina un BienServicioCargado específico.
     */
    @Query("""
           MATCH (s:Sale)-[r:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)-[:CON_ITEM_CARGADO]->(i:Item)
           WHERE s.id = $saleId AND bsc.id = $bienServicioCargadoId
           DETACH DELETE bsc
           RETURN s.id
           """)
    Mono<UUID> deleteBienServicioCargado(UUID saleId, UUID bienServicioCargadoId);

    /**
     * Elimina todos los BienServicioCargado de un Sale.
     */
    @Query("""
           MATCH (s:Sale)-[r:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           WHERE s.id = $saleId
           DETACH DELETE bsc
           RETURN s.id
           """)
    Mono<UUID> deleteAllBienServicioCargado(UUID saleId);

    /**
     * Calcula y actualiza los montos totales de un Sale basado en sus BienServicioCargado.
     */
    @Query("""
           MATCH (s:Sale) WHERE s.id = $saleId
           OPTIONAL MATCH (s)-[:CON_BIEN_SERVICIO_CARGADO]->(bsc:BienServicioCargado)
           WITH s,
                sum(bsc.precioInicial * bsc.cantidad) as totalInicial,
                sum(bsc.montoAcordado) as totalAcordado
           SET s.totalMontoInicial = COALESCE(totalInicial, 0.0),
               s.totalMontoAcordado = COALESCE(totalAcordado, 0.0)
           RETURN s
           """)
    Mono<Sale> calculateAndUpdateTotals(UUID saleId);

}
