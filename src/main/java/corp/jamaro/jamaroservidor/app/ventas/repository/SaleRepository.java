package corp.jamaro.jamaroservidor.app.ventas.repository;

import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Repository
public interface SaleRepository extends ReactiveNeo4jRepository<Sale, UUID> {

}
